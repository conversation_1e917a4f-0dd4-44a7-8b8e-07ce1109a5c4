<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="icon" type="image/x-icon" href="favicon.png">
    <meta name="viewport" content="initial-scale=1,user-scalable=no,maximum-scale=1,width=device-width">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="description" content="">
    <meta name="keywords" content="">
    <meta name="author" content="Hasanvand">
    <title class="title">نهضت ملی مسکن شهر سهند</title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="./assets/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/FontAwesome.Pro.6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./assets/leaflet/leaflet.css">
    <link rel="stylesheet" href="./assets/leaflet-sidebar/L.Control.Sidebar.css">
    <link rel="stylesheet" href="./assets/leaflet-draw/leaflet.draw.css">
    <link rel="stylesheet" href="./assets/leaflet-toolbar/leaflet.toolbar.css">
    <link rel="stylesheet" href="./assets/leaflet-measure/leaflet-measure.css">
    <link rel="stylesheet" href="./assets/leaflet-hash/leaflet-hash.css">
    <link rel="stylesheet" href="./assets/leaflet-groupedlayercontrol/leaflet.groupedlayercontrol.min.css">
    <link rel="stylesheet" href="./assets/leaflet-loading/Control.Loading.css">
    <link rel="stylesheet" href="./assets/leaflet-locatecontrol/L.Control.Locate.min.css">
    <link rel="stylesheet" href="./assets/leaflet-search/leaflet-search.min.css">
    <link rel="stylesheet" href="./assets/leaflet-easybutton/easy-button.css">
    <link rel="stylesheet" href="./assets/leaflet-coordinated-image-preview/leaflet-coordinated-image-preview.css">
    <link rel="stylesheet" href="./css/app.css">
</head>
<body>
    <div id="loading">
        <div class="loading-indicator">
            <div class="spinner"></div>
            <div class="loading-text">در حال بارگیری...</div>
        </div>
    </div>

    <div id="container">
        <div id="navbar-container"></div>
        <div id="sidebar"></div>
        <div id="map"></div>
        <div id="coordinatedImagePreviewControlContainer"></div>
    </div>
</body>
</html>
