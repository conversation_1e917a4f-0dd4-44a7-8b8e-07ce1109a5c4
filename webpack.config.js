const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const fs = require('fs');
const gracefulFs = require('graceful-fs');
gracefulFs.gracefulify(fs);

module.exports = {
    entry: './src/js/app.js',
    output: {
        filename: 'bundle.js',
        path: path.resolve(__dirname, 'dist'),
        publicPath: './'
    },
    resolve: {
        alias: {
            '@config': path.resolve(__dirname, 'src/config'),
            '@js': path.resolve(__dirname, 'src/js'),
            '@templates': path.resolve(__dirname, 'src/templates'),
            '@assets': path.resolve(__dirname, 'assets')
        }
    },
    module: {
        rules: [
            {
                test: /\.html$/,
                use: ['html-loader']
            },
            {
                test: /\.css$/,
                use: ['style-loader', 'css-loader']
            },
            {
                test: /\.(png|jpg|jpeg|gif|woff|woff2|ttf)$/i,
                type: 'asset/resource'
            },
            {
                test: /\.svg$/i,
                exclude: /assets\/FontAwesome\.Pro\.\d+\.\d+\.\d+\/svgs/,
                type: 'asset/resource'
            }
        ]
    },
    plugins: [
        new CopyPlugin({
            patterns: [
                { from: "assets", to: "assets" },
                { from: "Data", to: "Data" },
                { from: "src/images", to: "images" },
                { from: "src/css", to: "css" },
                { from: "src/templates", to: "templates" },
                { from: "src/config", to: "config" },
                { from: "public/web.config", to: "web.config", noErrorOnMissing: true },
                { from: "public/.htaccess", to: ".htaccess", noErrorOnMissing: true },
                { from: "public/favicon.png", to: "favicon.png", noErrorOnMissing: true },
            ],
        }),
        new HtmlWebpackPlugin({
            template: './public/index.html',
            filename: 'index.html',
            inject: 'body'
        })
    ],
    optimization: {
        minimize: false
    },
    devServer: {
        static: [
            {
                directory: path.join(__dirname, 'assets'),
                publicPath: '/assets'
            },
            {
                directory: path.join(__dirname, 'Data'),
                publicPath: '/Data'
            },
            {
                directory: path.join(__dirname, 'src/images'),
                publicPath: '/images'
            },
            {
                directory: path.join(__dirname, 'src/css'),
                publicPath: '/css'
            },
            {
                directory: path.join(__dirname, 'src/templates'),
                publicPath: '/templates'
            },
            {
                directory: path.join(__dirname, 'src/config'),
                publicPath: '/config'
            }
        ],
        port: 9000,
        hot: true,
        open: {
            app: {
                name: 'chrome'
            }
        },
        historyApiFallback: true,
        compress: true
    },
    cache: {
        type: 'filesystem',
        buildDependencies: {
            config: [__filename]
        }
    },
    stats: {
        children: true
    },
    performance: {
        maxAssetSize: 15000000,
        maxEntrypointSize: 15000000,
        hints: false
    }
};
