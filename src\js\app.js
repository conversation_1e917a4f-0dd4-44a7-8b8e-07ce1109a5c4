import { loadStyles } from "@config/styles.js";
import { loadScripts } from "@config/scripts.js";
import { fitToBounds } from "@js/map.js";
import { setupMap, setupUI, switchView } from "@js/ui.js";
// import { buildFilters, applyFilter } from "@js/filters.js";
// import { drawCharts } from "@js/charts.js";
import { getMap, setLayersConfig, getLayersConfig, getFeatureLayers } from "@js/config.js";
import { loadTemplate } from "@js/utils.js";
import { initializeWidgetToolbar } from "@js/widget-toolbar.js";

console.log("app.js لود شد و اجرا شد!");

// تست ساده برای اطمینان از کارکرد
document.addEventListener('DOMContentLoaded', function() {
    const appDiv = document.getElementById('app');
    if (appDiv) {
        appDiv.innerHTML = '<h1 style="color: red; text-align: center; margin-top: 50px;">✅ Webpack Dev Server کار می‌کند!</h1>';
        console.log("✅ DOM آماده است و app.js اجرا شد");
    } else {
        console.error("❌ عنصر #app پیدا نشد");
    }
});

// سرکوب کردن هشدارهای alasql برای بهبود تجربه کاربری
const originalError = console.error;
console.error = function(...args) {
    if (args[0] && typeof args[0] === 'string' && args[0].includes('unreachable code after return statement')) {
        return; // نادیده گرفتن این خطای خاص alasql
    }
    originalError.apply(console, args);
};

// سرکوب کردن هشدارهای touchleave برای بهبود تجربه کاربری
const originalWarn = console.warn;
console.warn = function(...args) {
    if (args[0] && typeof args[0] === 'string' && 
        (args[0].includes('wrong event specified: touchleave') || 
         args[0].includes('Deprecated include of L.Mixin.Events'))) {
        return; // نادیده گرفتن این هشدارهای خاص
    }
    originalWarn.apply(console, args);
};

// اجرای مستقیم برنامه بدون بررسی احراز هویت
initializeApp();

// تابع اصلی راه‌اندازی برنامه
function initializeApp() {
    console.log("برنامه در حال اجرا...");

    // نمایش لودر
    const loadingMask = document.createElement('div');
    loadingMask.innerHTML = `
        <div id="loading-mask" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.9); z-index: 9999; display: flex; justify-content: center; align-items: center; flex-direction: column;">
            <img src="./images/LoftLoader-Pro.gif" alt="Loading..." style="width: 100px; height: 100px; margin-bottom: 20px;">
            <div style="font-family:Nahid,Tahoma, sans-serif,Arial ; font-size: 16px; color: #333;">لطفا صبر کنید، در حال بارگذاری برنامه...</div>
        </div>
    `;
    document.body.appendChild(loadingMask);

    // لود jQuery و Leaflet و سایر وابستگی‌ها
    loadJQuery()
        .then(() => Promise.all([loadStyles(), loadScripts()]))
        .then(() => loadLeaflet())
        .then(() => loadInitialTemplates())
        .then(() => $.getJSON("./config/config.json"))
        .then((data) => setLayersConfig(data.layers))
        .then(() => setupMap())
        .then(() => setupUI())
        .then(() => {
            fitToBounds();
            switchView("map");

            // راه‌اندازی نوار تولبار ویجت‌ها
            initializeWidgetToolbar();

            // نمایش نام کاربر
            const username = localStorage.getItem("username") || "مهمان";
            $("#username").text(`کاربر: ${username}`);

            // رویداد خروج
            $("#logout-btn").on("click", () => {
                localStorage.removeItem("username");
                window.location.href = "/index.html";
            });

            // مخفی کردن لودینگ
            setTimeout(() => {
                $("#loading-mask").fadeOut(500, () => $("#loading-mask").remove());
            }, 2000);
        })
        .catch((error) => {
            console.error("خطا در فرآیند راه‌اندازی:", error);
            $("#loading-mask").remove();
        });
}

// لود jQuery
function loadJQuery() {
    return new Promise((resolve, reject) => {
        if (window.$) return resolve();
        const script = document.createElement("script");
        script.src = "./assets/jquery@2.2.4/jquery/dist/jquery.min.js";
        script.onload = resolve;
        script.onerror = () => reject(new Error("خطا در لود jQuery"));
        document.head.appendChild(script);
    });
}

// لود Leaflet
function loadLeaflet() {
    return new Promise((resolve, reject) => {
        if (window.L) return resolve();
        const script = document.createElement("script");
        script.src = "./assets/leaflet@1.9.4/dist/leaflet.js";
        script.onload = resolve;
        script.onerror = () => reject(new Error("خطا در لود Leaflet"));
        document.head.appendChild(script);
    });
}

// لود تمپلیت‌های اولیه
async function loadInitialTemplates() {
    return Promise.all([
        loadTemplate("navbar", null).then((data) => $("body").prepend(data)),
        loadTemplate("sidebar", null).then((data) => $("body").append(data)),
        loadTemplate("mapContainer", null).then((data) => $("body").append(data)),
        loadTemplate("tableContainer", null).then((data) => $("body").append(data)),
        loadTemplate("images", null).then((data) => $("body").append(data)),
        loadTemplate("aboutModal", null).then((data) => $("body").append(data)),
        loadTemplate("aboutModal1", null).then((data) => $("body").append(data)),
        loadTemplate("chartModal", null).then((data) => $("body").append(data)),
        loadTemplate("filterModal", null).then((data) => $("body").append(data)),
        loadTemplate("featureModal", null).then((data) => $("body").append(data)),
        loadTemplate("imageGalleryModal", null).then((data) => $("body").append(data)),
    ]);
}
